package com.kienlongbank.app_sale.handler;

import com.kienlongbank.app_sale.dto.request.CreateUserRequest;
import com.kienlongbank.app_sale.dto.response.UserResponse;
import com.kienlongbank.app_sale.entity.User;
import com.kienlongbank.app_sale.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <PERSON><PERSON> for creating a new user
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateUserHandler extends BaseHandler<CreateUserRequest, UserResponse> {

    private final UserService userService;

    @Override
    public UserResponse handle(CreateUserRequest request) {
        try {
            validateRequest(request);
            logRequestStart(request);
            
            // Create user
            User user = userService.createUser(request);
            
            // Convert to response
            UserResponse response = UserResponse.fromEntity(user);
            
            logRequestComplete(response);
            return response;
            
        } catch (Exception e) {
            logRequestError(request, e);
            throw e;
        }
    }

    @Override
    protected void validateRequest(CreateUserRequest request) {
        super.validateRequest(request);
        
        if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }
        
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        
        if (request.getFirstName() == null || request.getFirstName().trim().isEmpty()) {
            throw new IllegalArgumentException("First name cannot be null or empty");
        }
        
        if (request.getLastName() == null || request.getLastName().trim().isEmpty()) {
            throw new IllegalArgumentException("Last name cannot be null or empty");
        }
    }
}
