package com.kienlongbank.app_sale.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kienlongbank.app_sale.constant.ResponseCode;
import com.kienlongbank.app_sale.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * Base client class for external API calls using RestTemplate
 */
@Slf4j
public abstract class BaseClient {

    @Autowired
    protected RestTemplate restTemplate;

    @Autowired
    protected ObjectMapper objectMapper;

    /**
     * Call external API (third-party services like Bank6, Mango)
     */
    protected <T> T callApiExternal(String endpoint, HttpHeaders headers, Object request, Class<T> responseType) {
        return callApi(endpoint, headers, request, responseType, "EXTERNAL");
    }

    /**
     * Call internal API (internal microservices)
     */
    protected <T> T callApiInternal(String endpoint, HttpHeaders headers, Object request, Class<T> responseType) {
        return callApi(endpoint, headers, request, responseType, "INTERNAL");
    }

    /**
     * Generic method to call APIs
     */
    private <T> T callApi(String endpoint, HttpHeaders headers, Object request, Class<T> responseType, String serviceType) {
        try {
            // Log request
            logRequest(endpoint, headers, request, serviceType);

            // Prepare headers
            HttpHeaders requestHeaders = prepareHeaders(headers);

            // Prepare request entity
            HttpEntity<?> requestEntity = new HttpEntity<>(request, requestHeaders);

            // Determine HTTP method
            HttpMethod method = (request != null) ? HttpMethod.POST : HttpMethod.GET;

            // Make API call
            ResponseEntity<T> response = restTemplate.exchange(
                    endpoint,
                    method,
                    requestEntity,
                    responseType
            );

            // Log response
            logResponse(endpoint, response, serviceType);

            return response.getBody();

        } catch (HttpClientErrorException ex) {
            log.error("{} API Client Error - Endpoint: {}, Status: {}, Body: {}",
                    serviceType, endpoint, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw mapHttpClientException(ex, serviceType);

        } catch (HttpServerErrorException ex) {
            log.error("{} API Server Error - Endpoint: {}, Status: {}, Body: {}",
                    serviceType, endpoint, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw mapHttpServerException(ex, serviceType);

        } catch (ResourceAccessException ex) {
            log.error("{} API Timeout/Connection Error - Endpoint: {}, Error: {}",
                    serviceType, endpoint, ex.getMessage());
            throw mapResourceAccessException(ex, serviceType);

        } catch (Exception ex) {
            log.error("{} API Unexpected Error - Endpoint: {}, Error: {}",
                    serviceType, endpoint, ex.getMessage(), ex);
            throw mapGenericException(ex, serviceType);
        }
    }

    /**
     * Prepare common headers
     */
    private HttpHeaders prepareHeaders(HttpHeaders customHeaders) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
        headers.set("User-Agent", "AjeSaleApp/1.0");

        // Add custom headers if provided
        if (customHeaders != null) {
            headers.addAll(customHeaders);
        }

        return headers;
    }

    /**
     * Create headers with API key
     */
    protected HttpHeaders createHeadersWithApiKey(String apiKey) {
        HttpHeaders headers = new HttpHeaders();
        if (apiKey != null && !apiKey.isEmpty()) {
            headers.set("X-API-Key", apiKey);
        }
        return headers;
    }

    /**
     * Create headers with authorization token
     */
    protected HttpHeaders createHeadersWithAuth(String token) {
        HttpHeaders headers = new HttpHeaders();
        if (token != null && !token.isEmpty()) {
            headers.set("Authorization", "Bearer " + token);
        }
        return headers;
    }

    /**
     * Log request details
     */
    private void logRequest(String endpoint, HttpHeaders headers, Object request, String serviceType) {
        try {
            String requestBody = (request != null) ? objectMapper.writeValueAsString(request) : "null";
            log.info("{} API Request - Endpoint: {}, Headers: {}, Body: {}",
                    serviceType, endpoint, headers.toSingleValueMap(), requestBody);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize request body for logging: {}", e.getMessage());
            log.info("{} API Request - Endpoint: {}, Headers: {}, Body: [serialization failed]",
                    serviceType, endpoint, headers.toSingleValueMap());
        }
    }

    /**
     * Log response details
     */
    private void logResponse(String endpoint, ResponseEntity<?> response, String serviceType) {
        try {
            String responseBody = (response.getBody() != null) ?
                    objectMapper.writeValueAsString(response.getBody()) : "null";
            log.info("{} API Response - Endpoint: {}, Status: {}, Body: {}",
                    serviceType, endpoint, response.getStatusCode(), responseBody);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize response body for logging: {}", e.getMessage());
            log.info("{} API Response - Endpoint: {}, Status: {}, Body: [serialization failed]",
                    serviceType, endpoint, response.getStatusCode());
        }
    }

    /**
     * Map HTTP client exceptions (4xx errors)
     */
    private ApiException mapHttpClientException(HttpClientErrorException ex, String serviceType) {
        String errorCode = getServiceErrorCode(serviceType);

        HttpStatus status = ex.getStatusCode();
        if (status == HttpStatus.BAD_REQUEST) {
            return new ApiException(ResponseCode.INVALID_REQUEST, HttpStatus.BAD_REQUEST);
        } else if (status == HttpStatus.UNAUTHORIZED) {
            return new ApiException(ResponseCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
        } else if (status == HttpStatus.FORBIDDEN) {
            return new ApiException(ResponseCode.FORBIDDEN, HttpStatus.FORBIDDEN);
        } else if (status == HttpStatus.NOT_FOUND) {
            return new ApiException(ResponseCode.RESOURCE_NOT_FOUND, HttpStatus.NOT_FOUND);
        } else if (status == HttpStatus.CONFLICT) {
            return new ApiException(ResponseCode.RESOURCE_CONFLICT, HttpStatus.CONFLICT);
        } else {
            return new ApiException(errorCode, HttpStatus.BAD_GATEWAY);
        }
    }

    /**
     * Map HTTP server exceptions (5xx errors)
     */
    private ApiException mapHttpServerException(HttpServerErrorException ex, String serviceType) {
        String errorCode = getServiceErrorCode(serviceType);
        return new ApiException(errorCode, HttpStatus.BAD_GATEWAY);
    }

    /**
     * Map resource access exceptions (timeout, connection errors)
     */
    private ApiException mapResourceAccessException(ResourceAccessException ex, String serviceType) {
        String timeoutCode = getServiceTimeoutCode(serviceType);
        return new ApiException(timeoutCode, HttpStatus.GATEWAY_TIMEOUT);
    }

    /**
     * Map generic exceptions
     */
    private ApiException mapGenericException(Exception ex, String serviceType) {
        String errorCode = getServiceErrorCode(serviceType);
        return new ApiException(errorCode, HttpStatus.INTERNAL_SERVER_ERROR, ex);
    }

    /**
     * Get service-specific error code
     */
    private String getServiceErrorCode(String serviceType) {
        if ("EXTERNAL".equals(serviceType)) {
            return ResponseCode.EXTERNAL_SERVICE_ERROR;
        } else if ("INTERNAL".equals(serviceType)) {
            return ResponseCode.INTERNAL_SERVICE_ERROR;
        } else {
            return ResponseCode.EXTERNAL_SERVICE_ERROR;
        }
    }

    /**
     * Get service-specific timeout code
     */
    private String getServiceTimeoutCode(String serviceType) {
        if ("EXTERNAL".equals(serviceType)) {
            return ResponseCode.EXTERNAL_SERVICE_TIMEOUT;
        } else if ("INTERNAL".equals(serviceType)) {
            return ResponseCode.EXTERNAL_SERVICE_TIMEOUT;
        } else {
            return ResponseCode.EXTERNAL_SERVICE_TIMEOUT;
        }
    }
}
