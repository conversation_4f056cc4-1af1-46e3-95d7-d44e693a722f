package com.kienlongbank.app_sale.client;

import com.kienlongbank.app_sale.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * Base client class for external API calls
 */
@Slf4j
public abstract class BaseClient {

    protected final WebClient webClient;
    protected final String baseUrl;
    protected final Duration timeout;

    protected BaseClient(WebClient.Builder webClientBuilder, String baseUrl, Duration timeout) {
        this.baseUrl = baseUrl;
        this.timeout = timeout;
        this.webClient = webClientBuilder
                .baseUrl(baseUrl)
                .build();
    }

    /**
     * Handle WebClient response exceptions
     */
    protected <T> Mono<T> handleResponse(Mono<T> responseMono, String operation) {
        return responseMono
                .timeout(timeout)
                .doOnSubscribe(subscription -> log.debug("Starting {} operation", operation))
                .doOnSuccess(result -> log.debug("Successfully completed {} operation", operation))
                .onErrorMap(WebClientResponseException.class, ex -> {
                    log.error("Error in {} operation: Status={}, Body={}", operation, ex.getStatusCode(), ex.getResponseBodyAsString());
                    return mapWebClientException(ex, operation);
                })
                .onErrorMap(Exception.class, ex -> {
                    if (!(ex instanceof ApiException)) {
                        log.error("Unexpected error in {} operation", operation, ex);
                        return ApiException.internalServerError("External service call failed: " + operation);
                    }
                    return ex;
                });
    }

    /**
     * Map WebClientResponseException to ApiException
     */
    private ApiException mapWebClientException(WebClientResponseException ex, String operation) {
        HttpStatus status = (HttpStatus) ex.getStatusCode();
        String message = String.format("External service error in %s: %s", operation, ex.getMessage());
        
        return switch (status) {
            case BAD_REQUEST -> ApiException.badRequest(message);
            case UNAUTHORIZED -> ApiException.unauthorized(message);
            case FORBIDDEN -> ApiException.forbidden(message);
            case NOT_FOUND -> ApiException.notFound(message);
            case CONFLICT -> ApiException.conflict(message);
            case SERVICE_UNAVAILABLE -> ApiException.serviceUnavailable(message);
            default -> ApiException.internalServerError(message);
        };
    }

    /**
     * Create common headers for requests
     */
    protected WebClient.RequestHeadersSpec<?> addCommonHeaders(WebClient.RequestHeadersSpec<?> spec, String apiKey) {
        return spec
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .header("User-Agent", "AjeSaleApp/1.0")
                .header("X-API-Key", apiKey);
    }

    /**
     * Log request details
     */
    protected void logRequest(String method, String path, Object requestBody) {
        log.info("External API Request - Method: {}, URL: {}{}, Body: {}", 
                method, baseUrl, path, requestBody);
    }

    /**
     * Log response details
     */
    protected void logResponse(String method, String path, Object responseBody) {
        log.info("External API Response - Method: {}, URL: {}{}, Body: {}", 
                method, baseUrl, path, responseBody);
    }
}
