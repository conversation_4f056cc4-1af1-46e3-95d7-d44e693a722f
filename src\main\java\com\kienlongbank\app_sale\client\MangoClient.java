package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * Client for Mango external service
 */
@Slf4j
@Component
public class MangoClient extends BaseClient {

    private final String apiKey;

    public MangoClient(WebClient.Builder webClientBuilder,
                      @Value("${app.external-services.mango.base-url}") String baseUrl,
                      @Value("${app.external-services.mango.timeout}") long timeoutMs,
                      @Value("${app.external-services.mango.api-key}") String apiKey) {
        super(webClientBuilder, baseUrl, Duration.ofMillis(timeoutMs));
        this.apiKey = apiKey;
    }

    /**
     * Send notification via Mango
     */
    public Mono<Map<String, Object>> sendNotification(Map<String, Object> notificationRequest) {
        String path = "/api/notifications/send";
        logRequest("POST", path, notificationRequest);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(notificationRequest)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "sendNotification"
        );
    }

    /**
     * Send SMS via Mango
     */
    public Mono<Map<String, Object>> sendSms(String phoneNumber, String message) {
        String path = "/api/sms/send";
        Map<String, Object> smsRequest = Map.of(
                "phoneNumber", phoneNumber,
                "message", message
        );
        logRequest("POST", path, smsRequest);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(smsRequest)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "sendSms"
        );
    }

    /**
     * Send email via Mango
     */
    public Mono<Map<String, Object>> sendEmail(String email, String subject, String content) {
        String path = "/api/email/send";
        Map<String, Object> emailRequest = Map.of(
                "email", email,
                "subject", subject,
                "content", content
        );
        logRequest("POST", path, emailRequest);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(emailRequest)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "sendEmail"
        );
    }

    /**
     * Get notification status from Mango
     */
    public Mono<Map<String, Object>> getNotificationStatus(String notificationId) {
        String path = "/api/notifications/" + notificationId + "/status";
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getNotificationStatus"
        );
    }

    /**
     * Get delivery report from Mango
     */
    public Mono<Map<String, Object>> getDeliveryReport(String messageId) {
        String path = "/api/reports/delivery/" + messageId;
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getDeliveryReport"
        );
    }
}
