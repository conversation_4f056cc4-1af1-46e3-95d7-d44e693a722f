package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Client for Mango external service
 */
@Slf4j
@Component
public class MangoClient extends BaseClient {

    private final String baseUrl;
    private final String apiKey;

    public MangoClient(@Value("${app.external-services.mango.base-url}") String baseUrl,
                      @Value("${app.external-services.mango.api-key}") String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    /**
     * Send notification via Mango
     */
    public Map<String, Object> sendNotification(Map<String, Object> notificationRequest) {
        String endpoint = baseUrl + "/api/notifications/send";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, notificationRequest, Map.class);
    }

    /**
     * Send SMS via Mango
     */
    public Map<String, Object> sendSms(String phoneNumber, String message) {
        String endpoint = baseUrl + "/api/sms/send";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        Map<String, Object> smsRequest = Map.of(
                "phoneNumber", phoneNumber,
                "message", message
        );

        return callApiExternal(endpoint, headers, smsRequest, Map.class);
    }

    /**
     * Send email via Mango
     */
    public Map<String, Object> sendEmail(String email, String subject, String content) {
        String endpoint = baseUrl + "/api/email/send";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        Map<String, Object> emailRequest = Map.of(
                "email", email,
                "subject", subject,
                "content", content
        );

        return callApiExternal(endpoint, headers, emailRequest, Map.class);
    }

    /**
     * Get notification status from Mango
     */
    public Map<String, Object> getNotificationStatus(String notificationId) {
        String endpoint = baseUrl + "/api/notifications/" + notificationId + "/status";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, null, Map.class);
    }

    /**
     * Get delivery report from Mango
     */
    public Map<String, Object> getDeliveryReport(String messageId) {
        String endpoint = baseUrl + "/api/reports/delivery/" + messageId;
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, null, Map.class);
    }
}
