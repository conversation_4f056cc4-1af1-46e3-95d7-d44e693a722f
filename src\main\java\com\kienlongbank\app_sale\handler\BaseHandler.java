package com.kienlongbank.app_sale.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Base handler class with common functionality
 */
@Slf4j
public abstract class BaseHandler<T, R> {

    @Autowired
    protected ApplicationEventPublisher eventPublisher;

    /**
     * Handle the request
     * @param request the request object
     * @return the response object
     */
    public abstract R handle(T request);

    /**
     * Validate the request before processing
     * @param request the request object
     */
    protected void validateRequest(T request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
    }

    /**
     * Log the start of request processing
     * @param request the request object
     */
    protected void logRequestStart(T request) {
        log.info("Starting to process request: {}", request.getClass().getSimpleName());
    }

    /**
     * Log the completion of request processing
     * @param response the response object
     */
    protected void logRequestComplete(R response) {
        log.info("Completed processing request with response: {}", response.getClass().getSimpleName());
    }

    /**
     * Log error during request processing
     * @param request the request object
     * @param error the error that occurred
     */
    protected void logRequestError(T request, Exception error) {
        log.error("Error processing request: {} - Error: {}", 
                request.getClass().getSimpleName(), error.getMessage(), error);
    }
}
