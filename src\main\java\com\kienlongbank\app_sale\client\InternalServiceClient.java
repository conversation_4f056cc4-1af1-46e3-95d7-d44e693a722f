package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * Client for Internal Service
 */
@Slf4j
@Component
public class InternalServiceClient extends BaseClient {

    private final String apiKey;

    public InternalServiceClient(WebClient.Builder webClientBuilder,
                               @Value("${app.external-services.internal-service.base-url}") String baseUrl,
                               @Value("${app.external-services.internal-service.timeout}") long timeoutMs,
                               @Value("${app.external-services.internal-service.api-key}") String apiKey) {
        super(webClientBuilder, baseUrl, Duration.ofMillis(timeoutMs));
        this.apiKey = apiKey;
    }

    /**
     * Get user profile from internal service
     */
    public Mono<Map<String, Object>> getUserProfile(String userId) {
        String path = "/api/users/" + userId + "/profile";
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getUserProfile"
        );
    }

    /**
     * Update user profile in internal service
     */
    public Mono<Map<String, Object>> updateUserProfile(String userId, Map<String, Object> profileData) {
        String path = "/api/users/" + userId + "/profile";
        logRequest("PUT", path, profileData);
        
        return handleResponse(
                addCommonHeaders(webClient.put().uri(path), apiKey)
                        .bodyValue(profileData)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("PUT", path, response)),
                "updateUserProfile"
        );
    }

    /**
     * Get system configuration from internal service
     */
    public Mono<Map<String, Object>> getSystemConfig(String configKey) {
        String path = "/api/config/" + configKey;
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getSystemConfig"
        );
    }

    /**
     * Log audit event to internal service
     */
    public Mono<Map<String, Object>> logAuditEvent(Map<String, Object> auditData) {
        String path = "/api/audit/log";
        logRequest("POST", path, auditData);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(auditData)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "logAuditEvent"
        );
    }

    /**
     * Validate business rules via internal service
     */
    public Mono<Map<String, Object>> validateBusinessRules(Map<String, Object> validationRequest) {
        String path = "/api/validation/business-rules";
        logRequest("POST", path, validationRequest);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(validationRequest)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "validateBusinessRules"
        );
    }

    /**
     * Get reference data from internal service
     */
    public Mono<Map<String, Object>> getReferenceData(String dataType) {
        String path = "/api/reference-data/" + dataType;
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getReferenceData"
        );
    }
}
