package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Client for Internal Service
 */
@Slf4j
@Component
public class InternalServiceClient extends BaseClient {

    private final String baseUrl;
    private final String apiKey;

    public InternalServiceClient(@Value("${app.external-services.internal-service.base-url}") String baseUrl,
                               @Value("${app.external-services.internal-service.api-key}") String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    /**
     * Get user profile from internal service
     */
    public Map<String, Object> getUserProfile(String userId) {
        String endpoint = baseUrl + "/api/users/" + userId + "/profile";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, null, Map.class);
    }

    /**
     * Update user profile in internal service
     */
    public Map<String, Object> updateUserProfile(String userId, Map<String, Object> profileData) {
        String endpoint = baseUrl + "/api/users/" + userId + "/profile";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, profileData, Map.class);
    }

    /**
     * Get system configuration from internal service
     */
    public Map<String, Object> getSystemConfig(String configKey) {
        String endpoint = baseUrl + "/api/config/" + configKey;
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, null, Map.class);
    }

    /**
     * Log audit event to internal service
     */
    public Map<String, Object> logAuditEvent(Map<String, Object> auditData) {
        String endpoint = baseUrl + "/api/audit/log";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, auditData, Map.class);
    }

    /**
     * Validate business rules via internal service
     */
    public Map<String, Object> validateBusinessRules(Map<String, Object> validationRequest) {
        String endpoint = baseUrl + "/api/validation/business-rules";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, validationRequest, Map.class);
    }

    /**
     * Get reference data from internal service
     */
    public Map<String, Object> getReferenceData(String dataType) {
        String endpoint = baseUrl + "/api/reference-data/" + dataType;
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiInternal(endpoint, headers, null, Map.class);
    }
}
