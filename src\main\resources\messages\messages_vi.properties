# Vietnamese messages

# Success messages
API000=Thao tác hoàn thành thành công
API001=Tạo tài nguyên thành công
API002=Cập nhật tài nguyên thành công
API003=Xóa tài nguyên thành công

# General error messages
API100=Đã xảy ra lỗi không mong muốn
API101=Định dạng yêu cầu không hợp lệ
API102=X<PERSON>c thực thất bại
API103=Thiếu tham số bắt buộc
API104=Giá trị tham số không hợp lệ
API105=Vi phạm quy tắc nghiệp vụ

# Authentication & Authorization errors
API200=Yêu cầu xác thực
API201=Truy cập bị từ chối
API202=Token đã hết hạn
API203=Token không hợp lệ
API204=Không đủ quyền truy cập

# Resource errors
API300=Không tìm thấy tài nguyên
API301=Tài nguyên đã tồn tại
API302=Xung đột tài nguyên
API303=Tài nguyên đang bị khóa
API304=Tài nguyên đã hết hạn

# User management errors
API400=Không tìm thấy người dùng
API401=Người dùng đã tồn tại
API402=Người dùng không hoạt động
API403=Người dùng bị tạm ngưng
API404=Tên đăng nhập đã tồn tại
API405=Email đã tồn tại
API406=Trạng thái người dùng không hợp lệ

# External service errors
API500=Lỗi dịch vụ bên ngoài
API501=Hết thời gian chờ dịch vụ bên ngoài
API502=Dịch vụ bên ngoài không khả dụng
API510=Lỗi dịch vụ Bank6
API520=Lỗi dịch vụ Mango
API530=Lỗi dịch vụ nội bộ

# Database errors
API600=Đã xảy ra lỗi cơ sở dữ liệu
API601=Lỗi kết nối cơ sở dữ liệu
API602=Vi phạm ràng buộc cơ sở dữ liệu
API603=Hết thời gian chờ thao tác cơ sở dữ liệu
API604=Lỗi khóa lạc quan

# System errors
API700=Lỗi máy chủ nội bộ
API701=Dịch vụ không khả dụng
API702=Lỗi cấu hình
API703=Lỗi xử lý tệp
API704=Lỗi mã hóa

# Rate limiting & throttling
API800=Vượt quá giới hạn tốc độ
API801=Quá nhiều yêu cầu
API802=Vượt quá hạn ngạch

# Maintenance & operational
API900=Hệ thống đang bảo trì
API901=Tính năng đã bị vô hiệu hóa
API902=Phiên bản không được hỗ trợ

# User specific messages
user.created=Tạo người dùng thành công
user.updated=Cập nhật người dùng thành công
user.deleted=Xóa người dùng thành công
user.not.found=Không tìm thấy người dùng với ID: {0}
user.username.exists=Tên đăng nhập ''{0}'' đã tồn tại
user.email.exists=Email ''{0}'' đã tồn tại
user.status.changed=Trạng thái người dùng đã được thay đổi thành {0}

# Validation messages
validation.required=Trường này là bắt buộc
validation.email.invalid=Vui lòng nhập địa chỉ email hợp lệ
validation.username.length=Tên đăng nhập phải từ 3 đến 50 ký tự
validation.name.length=Tên không được vượt quá 50 ký tự
validation.phone.invalid=Vui lòng nhập số điện thoại hợp lệ
