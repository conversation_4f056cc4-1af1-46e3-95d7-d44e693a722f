package com.kienlongbank.app_sale.handler;

import com.kienlongbank.app_sale.dto.request.UpdateUserRequest;
import com.kienlongbank.app_sale.dto.response.UserResponse;
import com.kienlongbank.app_sale.entity.User;
import com.kienlongbank.app_sale.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <PERSON><PERSON> for updating a user
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateUserHandler extends BaseHandler<UpdateUserRequest, UserResponse> {

    private final UserService userService;
    private Long userId;

    public UserResponse handle(Long userId, UpdateUserRequest request) {
        this.userId = userId;
        return handle(request);
    }

    @Override
    public UserResponse handle(UpdateUserRequest request) {
        try {
            validateRequest(request);
            logRequestStart(request);
            
            // Update user
            User user = userService.updateUser(userId, request);
            
            // Convert to response
            UserResponse response = UserResponse.fromEntity(user);
            
            logRequestComplete(response);
            return response;
            
        } catch (Exception e) {
            logRequestError(request, e);
            throw e;
        }
    }

    @Override
    protected void validateRequest(UpdateUserRequest request) {
        super.validateRequest(request);
        
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }
        
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        
        if (request.getFirstName() == null || request.getFirstName().trim().isEmpty()) {
            throw new IllegalArgumentException("First name cannot be null or empty");
        }
        
        if (request.getLastName() == null || request.getLastName().trim().isEmpty()) {
            throw new IllegalArgumentException("Last name cannot be null or empty");
        }
    }
}
