package com.kienlongbank.app_sale.exception;

import com.kienlongbank.app_sale.constant.ResponseCode;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * Custom API Exception for handling business logic errors with i18n support
 */
@Getter
public class ApiException extends RuntimeException {

    private final String errorCode;
    private final HttpStatus httpStatus;
    private final Object data;
    private final Object[] messageArgs;

    public ApiException(String errorCode) {
        super(errorCode);
        this.errorCode = errorCode;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
        this.messageArgs = null;
    }

    public ApiException(String errorCode, Object[] messageArgs) {
        super(errorCode);
        this.errorCode = errorCode;
        this.httpStatus = HttpStatus.BAD_REQUEST;
        this.data = null;
        this.messageArgs = messageArgs;
    }

    public ApiException(String errorCode, HttpStatus httpStatus) {
        super(errorCode);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = null;
        this.messageArgs = null;
    }

    public ApiException(String errorCode, HttpStatus httpStatus, Object[] messageArgs) {
        super(errorCode);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = null;
        this.messageArgs = messageArgs;
    }

    public ApiException(String errorCode, HttpStatus httpStatus, Object data, Object[] messageArgs) {
        super(errorCode);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = data;
        this.messageArgs = messageArgs;
    }

    public ApiException(String errorCode, Throwable cause) {
        super(errorCode, cause);
        this.errorCode = errorCode;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
        this.messageArgs = null;
    }

    public ApiException(String errorCode, HttpStatus httpStatus, Throwable cause) {
        super(errorCode, cause);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = null;
        this.messageArgs = null;
    }

    // Common error factory methods
    public static ApiException badRequest() {
        return new ApiException(ResponseCode.INVALID_REQUEST, HttpStatus.BAD_REQUEST);
    }

    public static ApiException badRequest(Object[] messageArgs) {
        return new ApiException(ResponseCode.INVALID_REQUEST, HttpStatus.BAD_REQUEST, messageArgs);
    }

    public static ApiException notFound() {
        return new ApiException(ResponseCode.RESOURCE_NOT_FOUND, HttpStatus.NOT_FOUND);
    }

    public static ApiException notFound(Object[] messageArgs) {
        return new ApiException(ResponseCode.RESOURCE_NOT_FOUND, HttpStatus.NOT_FOUND, messageArgs);
    }

    public static ApiException unauthorized() {
        return new ApiException(ResponseCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
    }

    public static ApiException forbidden() {
        return new ApiException(ResponseCode.FORBIDDEN, HttpStatus.FORBIDDEN);
    }

    public static ApiException conflict() {
        return new ApiException(ResponseCode.RESOURCE_CONFLICT, HttpStatus.CONFLICT);
    }

    public static ApiException conflict(Object[] messageArgs) {
        return new ApiException(ResponseCode.RESOURCE_CONFLICT, HttpStatus.CONFLICT, messageArgs);
    }

    public static ApiException internalServerError() {
        return new ApiException(ResponseCode.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public static ApiException serviceUnavailable() {
        return new ApiException(ResponseCode.SERVICE_UNAVAILABLE, HttpStatus.SERVICE_UNAVAILABLE);
    }

    // User specific exceptions
    public static ApiException userNotFound(Object[] messageArgs) {
        return new ApiException(ResponseCode.USER_NOT_FOUND, HttpStatus.NOT_FOUND, messageArgs);
    }

    public static ApiException usernameExists(Object[] messageArgs) {
        return new ApiException(ResponseCode.USERNAME_ALREADY_EXISTS, HttpStatus.CONFLICT, messageArgs);
    }

    public static ApiException emailExists(Object[] messageArgs) {
        return new ApiException(ResponseCode.EMAIL_ALREADY_EXISTS, HttpStatus.CONFLICT, messageArgs);
    }

    // External service exceptions
    public static ApiException externalServiceError(String serviceCode) {
        return new ApiException(serviceCode, HttpStatus.BAD_GATEWAY);
    }

    public static ApiException externalServiceTimeout(String serviceCode) {
        return new ApiException(serviceCode, HttpStatus.GATEWAY_TIMEOUT);
    }

    // Database exceptions
    public static ApiException databaseError() {
        return new ApiException(ResponseCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public static ApiException optimisticLockError() {
        return new ApiException(ResponseCode.OPTIMISTIC_LOCK_ERROR, HttpStatus.CONFLICT);
    }
}
