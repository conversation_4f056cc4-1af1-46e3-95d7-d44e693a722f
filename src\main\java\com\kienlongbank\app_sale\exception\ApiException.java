package com.kienlongbank.app_sale.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * Custom API Exception for handling business logic errors
 */
@Getter
public class ApiException extends RuntimeException {
    
    private final String errorCode;
    private final HttpStatus httpStatus;
    private final Object data;
    
    public ApiException(String message) {
        super(message);
        this.errorCode = "GENERAL_ERROR";
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
    }
    
    public ApiException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = HttpStatus.BAD_REQUEST;
        this.data = null;
    }
    
    public ApiException(String message, String errorCode, HttpStatus httpStatus) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = null;
    }
    
    public ApiException(String message, String errorCode, HttpStatus httpStatus, Object data) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.data = data;
    }
    
    public ApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GENERAL_ERROR";
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
    }
    
    public ApiException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.data = null;
    }
    
    // Common error types
    public static ApiException badRequest(String message) {
        return new ApiException(message, "BAD_REQUEST", HttpStatus.BAD_REQUEST);
    }
    
    public static ApiException notFound(String message) {
        return new ApiException(message, "NOT_FOUND", HttpStatus.NOT_FOUND);
    }
    
    public static ApiException unauthorized(String message) {
        return new ApiException(message, "UNAUTHORIZED", HttpStatus.UNAUTHORIZED);
    }
    
    public static ApiException forbidden(String message) {
        return new ApiException(message, "FORBIDDEN", HttpStatus.FORBIDDEN);
    }
    
    public static ApiException conflict(String message) {
        return new ApiException(message, "CONFLICT", HttpStatus.CONFLICT);
    }
    
    public static ApiException internalServerError(String message) {
        return new ApiException(message, "INTERNAL_SERVER_ERROR", HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    public static ApiException serviceUnavailable(String message) {
        return new ApiException(message, "SERVICE_UNAVAILABLE", HttpStatus.SERVICE_UNAVAILABLE);
    }
}
