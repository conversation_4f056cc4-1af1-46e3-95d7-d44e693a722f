# Default messages (English)

# Success messages
API000=Operation completed successfully
API001=Resource created successfully
API002=Resource updated successfully
API003=Resource deleted successfully

# General error messages
API100=An unexpected error occurred
API101=Invalid request format
API102=Validation failed
API103=Required parameter is missing
API104=Invalid parameter value
API105=Business rule violation

# Authentication & Authorization errors
API200=Authentication required
API201=Access denied
API202=Token has expired
API203=Invalid token
API204=Insufficient privileges

# Resource errors
API300=Resource not found
API301=Resource already exists
API302=Resource conflict
API303=Resource is locked
API304=Resource has expired

# User management errors
API400=User not found
API401=User already exists
API402=User is inactive
API403=User is suspended
API404=Username already exists
API405=Email already exists
API406=Invalid user status

# External service errors
API500=External service error
API501=External service timeout
API502=External service unavailable
API510=Bank6 service error
API520=Mango service error
API530=Internal service error

# Database errors
API600=Database error occurred
API601=Database connection error
API602=Database constraint violation
API603=Database operation timeout
API604=Optimistic lock error

# System errors
API700=Internal server error
API701=Service unavailable
API702=Configuration error
API703=File processing error
API704=Encryption error

# Rate limiting & throttling
API800=Rate limit exceeded
API801=Too many requests
API802=Quota exceeded

# Maintenance & operational
API900=System under maintenance
API901=Feature is disabled
API902=Version not supported

# User specific messages
user.created=User created successfully
user.updated=User updated successfully
user.deleted=User deleted successfully
user.not.found=User not found with ID: {0}
user.username.exists=Username ''{0}'' already exists
user.email.exists=Email ''{0}'' already exists
user.status.changed=User status changed to {0}

# Validation messages
validation.required=This field is required
validation.email.invalid=Please enter a valid email address
validation.username.length=Username must be between 3 and 50 characters
validation.name.length=Name must not exceed 50 characters
validation.phone.invalid=Please enter a valid phone number
