package com.kienlongbank.app_sale.service;

import com.kienlongbank.app_sale.dto.request.CreateUserRequest;
import com.kienlongbank.app_sale.dto.request.UpdateUserRequest;
import com.kienlongbank.app_sale.entity.User;
import com.kienlongbank.app_sale.exception.ApiException;
import com.kienlongbank.app_sale.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for User operations
 */
@Slf4j
@Service
public class UserService extends BaseService<User, UserRepository> {

    public UserService(UserRepository repository) {
        super(repository);
    }

    /**
     * Create a new user
     */
    @Transactional
    public User createUser(CreateUserRequest request) {
        log.debug("Creating user with username: {}", request.getUsername());
        
        // Check if username already exists
        if (repository.existsByUsername(request.getUsername())) {
            throw ApiException.conflict("Username already exists: " + request.getUsername());
        }
        
        // Check if email already exists
        if (repository.existsByEmail(request.getEmail())) {
            throw ApiException.conflict("Email already exists: " + request.getEmail());
        }
        
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setDepartment(request.getDepartment());
        user.setPosition(request.getPosition());
        user.setStatus(request.getStatus());
        
        return save(user);
    }

    /**
     * Update an existing user
     */
    @Transactional
    public User updateUser(Long id, UpdateUserRequest request) {
        log.debug("Updating user with id: {}", id);
        
        User user = findByIdOrThrow(id);
        
        // Check if email already exists for another user
        Optional<User> existingUser = repository.findByEmail(request.getEmail());
        if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
            throw ApiException.conflict("Email already exists: " + request.getEmail());
        }
        
        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setDepartment(request.getDepartment());
        user.setPosition(request.getPosition());
        
        if (request.getStatus() != null) {
            user.setStatus(request.getStatus());
        }
        
        return update(user);
    }

    /**
     * Find user by username
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        return repository.findByUsername(username);
    }

    /**
     * Find user by email
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        log.debug("Finding user by email: {}", email);
        return repository.findByEmail(email);
    }

    /**
     * Find users by status
     */
    @Transactional(readOnly = true)
    public List<User> findByStatus(User.UserStatus status) {
        log.debug("Finding users by status: {}", status);
        return repository.findByStatus(status);
    }

    /**
     * Find users by department
     */
    @Transactional(readOnly = true)
    public List<User> findByDepartment(String department) {
        log.debug("Finding users by department: {}", department);
        return repository.findByDepartment(department);
    }

    /**
     * Search users by name or email
     */
    @Transactional(readOnly = true)
    public List<User> searchUsers(String searchTerm) {
        log.debug("Searching users with term: {}", searchTerm);
        return repository.searchUsers(searchTerm);
    }

    /**
     * Change user status
     */
    @Transactional
    public User changeUserStatus(Long id, User.UserStatus status) {
        log.debug("Changing user status for id: {} to: {}", id, status);
        
        User user = findByIdOrThrow(id);
        user.setStatus(status);
        
        return update(user);
    }

    @Override
    protected void validateEntity(User entity) {
        super.validateEntity(entity);
        
        if (entity.getUsername() == null || entity.getUsername().trim().isEmpty()) {
            throw ApiException.badRequest("Username cannot be null or empty");
        }
        
        if (entity.getEmail() == null || entity.getEmail().trim().isEmpty()) {
            throw ApiException.badRequest("Email cannot be null or empty");
        }
        
        if (entity.getFirstName() == null || entity.getFirstName().trim().isEmpty()) {
            throw ApiException.badRequest("First name cannot be null or empty");
        }
        
        if (entity.getLastName() == null || entity.getLastName().trim().isEmpty()) {
            throw ApiException.badRequest("Last name cannot be null or empty");
        }
    }
}
