package com.kienlongbank.app_sale.service;

import com.kienlongbank.app_sale.constant.ResponseCode;
import com.kienlongbank.app_sale.dto.request.CreateUserRequest;
import com.kienlongbank.app_sale.dto.request.UpdateUserRequest;
import com.kienlongbank.app_sale.entity.User;
import com.kienlongbank.app_sale.exception.ApiException;
import com.kienlongbank.app_sale.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for User operations with native SQL queries
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;

    /**
     * Find all active users
     */
    @Transactional(readOnly = true)
    public List<User> findAll() {
        log.debug("Finding all active users");
        return userRepository.findAllActive();
    }

    /**
     * Find all users with pagination
     */
    @Transactional(readOnly = true)
    public Page<User> findAll(Pageable pageable) {
        log.debug("Finding all users with pagination: {}", pageable);
        return userRepository.findAll(pageable);
    }

    /**
     * Find user by ID
     */
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        log.debug("Finding user by id: {}", id);
        return userRepository.findByIdActive(id);
    }

    /**
     * Find user by ID or throw exception
     */
    @Transactional(readOnly = true)
    public User findByIdOrThrow(Long id) {
        return findById(id)
                .orElseThrow(() -> ApiException.userNotFound(new Object[]{id}));
    }

    /**
     * Create a new user
     */
    @Transactional
    public User createUser(CreateUserRequest request) {
        log.debug("Creating user with username: {}", request.getUsername());

        // Check if username already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw ApiException.usernameExists(new Object[]{request.getUsername()});
        }

        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw ApiException.emailExists(new Object[]{request.getEmail()});
        }

        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setDepartment(request.getDepartment());
        user.setPosition(request.getPosition());
        user.setStatus(request.getStatus());

        validateUser(user);
        return userRepository.save(user);
    }

    /**
     * Update an existing user
     */
    @Transactional
    public User updateUser(Long id, UpdateUserRequest request) {
        log.debug("Updating user with id: {}", id);

        User user = findByIdOrThrow(id);

        // Check if email already exists for another user
        Optional<User> existingUser = userRepository.findByEmail(request.getEmail());
        if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
            throw ApiException.emailExists(new Object[]{request.getEmail()});
        }

        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setDepartment(request.getDepartment());
        user.setPosition(request.getPosition());

        if (request.getStatus() != null) {
            user.setStatus(request.getStatus());
        }

        validateUser(user);
        return userRepository.save(user);
    }

    /**
     * Delete user (soft delete)
     */
    @Transactional
    public void deleteById(Long id) {
        log.debug("Soft deleting user with id: {}", id);

        if (!userRepository.existsByIdActive(id)) {
            throw ApiException.userNotFound(new Object[]{id});
        }

        int deletedCount = userRepository.softDeleteById(id);
        if (deletedCount == 0) {
            throw new ApiException(ResponseCode.DATABASE_ERROR);
        }
    }

    /**
     * Check if user exists by ID
     */
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        log.debug("Checking if user exists with id: {}", id);
        return userRepository.existsByIdActive(id);
    }

    /**
     * Count active users
     */
    @Transactional(readOnly = true)
    public long count() {
        log.debug("Counting all active users");
        return userRepository.countActive();
    }

    /**
     * Find user by username
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        return userRepository.findByUsername(username);
    }

    /**
     * Find user by email
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        log.debug("Finding user by email: {}", email);
        return userRepository.findByEmail(email);
    }

    /**
     * Find users by status
     */
    @Transactional(readOnly = true)
    public List<User> findByStatus(User.UserStatus status) {
        log.debug("Finding users by status: {}", status);
        return userRepository.findByStatus(status.name());
    }

    /**
     * Find users by department
     */
    @Transactional(readOnly = true)
    public List<User> findByDepartment(String department) {
        log.debug("Finding users by department: {}", department);
        return userRepository.findByDepartment(department);
    }

    /**
     * Search users by name or email
     */
    @Transactional(readOnly = true)
    public List<User> searchUsers(String searchTerm) {
        log.debug("Searching users with term: {}", searchTerm);
        return userRepository.searchUsers(searchTerm);
    }

    /**
     * Change user status
     */
    @Transactional
    public User changeUserStatus(Long id, User.UserStatus status) {
        log.debug("Changing user status for id: {} to: {}", id, status);

        User user = findByIdOrThrow(id);
        user.setStatus(status);

        validateUser(user);
        return userRepository.save(user);
    }

    /**
     * Validate user entity
     */
    private void validateUser(User user) {
        if (user == null) {
            throw new ApiException(ResponseCode.INVALID_REQUEST);
        }

        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            throw new ApiException(ResponseCode.INVALID_PARAMETER);
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new ApiException(ResponseCode.INVALID_PARAMETER);
        }

        if (user.getFirstName() == null || user.getFirstName().trim().isEmpty()) {
            throw new ApiException(ResponseCode.INVALID_PARAMETER);
        }

        if (user.getLastName() == null || user.getLastName().trim().isEmpty()) {
            throw new ApiException(ResponseCode.INVALID_PARAMETER);
        }
    }
}
