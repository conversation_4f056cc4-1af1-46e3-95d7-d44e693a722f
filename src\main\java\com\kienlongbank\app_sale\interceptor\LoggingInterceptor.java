package com.kienlongbank.app_sale.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * Interceptor for logging HTTP requests and responses
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoggingInterceptor implements HandlerInterceptor {

    private final ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Generate request ID
        String requestId = UUID.randomUUID().toString();
        MDC.put("requestId", requestId);
        
        // Get username from JWT token
        String username = getCurrentUsername();
        MDC.put("username", username);
        
        // Log request
        logRequest(request, requestId);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                              Object handler, Exception ex) {
        try {
            // Log response
            logResponse(request, response);
        } finally {
            // Clear MDC
            MDC.clear();
        }
    }

    private void logRequest(HttpServletRequest request, String requestId) {
        try {
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("HTTP Request - ");
            logMessage.append("Method: ").append(request.getMethod()).append(", ");
            logMessage.append("URI: ").append(request.getRequestURI()).append(", ");
            logMessage.append("Query: ").append(request.getQueryString()).append(", ");
            logMessage.append("Remote IP: ").append(getClientIpAddress(request)).append(", ");
            logMessage.append("User-Agent: ").append(request.getHeader("User-Agent"));

            // Log request body for POST/PUT requests
            if (request instanceof ContentCachingRequestWrapper wrapper) {
                byte[] content = wrapper.getContentAsByteArray();
                if (content.length > 0) {
                    String requestBody = new String(content, StandardCharsets.UTF_8);
                    logMessage.append(", Body: ").append(requestBody);
                }
            }

            log.info(logMessage.toString());
        } catch (Exception e) {
            log.error("Error logging request", e);
        }
    }

    private void logResponse(HttpServletRequest request, HttpServletResponse response) {
        try {
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("HTTP Response - ");
            logMessage.append("Method: ").append(request.getMethod()).append(", ");
            logMessage.append("URI: ").append(request.getRequestURI()).append(", ");
            logMessage.append("Status: ").append(response.getStatus()).append(", ");
            logMessage.append("Content-Type: ").append(response.getContentType());

            // Log response body
            if (response instanceof ContentCachingResponseWrapper wrapper) {
                byte[] content = wrapper.getContentAsByteArray();
                if (content.length > 0) {
                    String responseBody = new String(content, StandardCharsets.UTF_8);
                    logMessage.append(", Body: ").append(responseBody);
                }
            }

            log.info(logMessage.toString());
        } catch (Exception e) {
            log.error("Error logging response", e);
        }
    }

    private String getCurrentUsername() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated()) {
                return "anonymous";
            }
            
            if (authentication.getPrincipal() instanceof Jwt jwt) {
                String username = jwt.getClaimAsString("preferred_username");
                return username != null ? username : "unknown";
            }
            
            return authentication.getName();
        } catch (Exception e) {
            log.debug("Error getting username", e);
            return "unknown";
        }
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
