package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Client for Bank6 external service
 */
@Slf4j
@Component
public class Bank6Client extends BaseClient {

    private final String baseUrl;
    private final String apiKey;

    public Bank6Client(@Value("${app.external-services.bank6.base-url}") String baseUrl,
                      @Value("${app.external-services.bank6.api-key}") String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    /**
     * Get account information from Bank6
     */
    public Map<String, Object> getAccountInfo(String accountNumber) {
        String endpoint = baseUrl + "/api/accounts/" + accountNumber;
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, null, Map.class);
    }

    /**
     * Transfer money via Bank6
     */
    public Map<String, Object> transferMoney(Map<String, Object> transferRequest) {
        String endpoint = baseUrl + "/api/transfers";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, transferRequest, Map.class);
    }

    /**
     * Get transaction history from Bank6
     */
    public Map<String, Object> getTransactionHistory(String accountNumber,
                                                    String fromDate,
                                                    String toDate) {
        String endpoint = String.format("%s/api/accounts/%s/transactions?from=%s&to=%s",
                                       baseUrl, accountNumber, fromDate, toDate);
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, null, Map.class);
    }

    /**
     * Check account balance from Bank6
     */
    public Map<String, Object> checkBalance(String accountNumber) {
        String endpoint = baseUrl + "/api/accounts/" + accountNumber + "/balance";
        HttpHeaders headers = createHeadersWithApiKey(apiKey);

        return callApiExternal(endpoint, headers, null, Map.class);
    }
}
