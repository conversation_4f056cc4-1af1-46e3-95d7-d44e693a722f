package com.kienlongbank.app_sale.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * Client for Bank6 external service
 */
@Slf4j
@Component
public class Bank6Client extends BaseClient {

    private final String apiKey;

    public Bank6Client(WebClient.Builder webClientBuilder,
                      @Value("${app.external-services.bank6.base-url}") String baseUrl,
                      @Value("${app.external-services.bank6.timeout}") long timeoutMs,
                      @Value("${app.external-services.bank6.api-key}") String apiKey) {
        super(webClientBuilder, baseUrl, Duration.ofMillis(timeoutMs));
        this.apiKey = apiKey;
    }

    /**
     * Get account information from Bank6
     */
    public Mono<Map<String, Object>> getAccountInfo(String accountNumber) {
        String path = "/api/accounts/" + accountNumber;
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getAccountInfo"
        );
    }

    /**
     * Transfer money via Bank6
     */
    public Mono<Map<String, Object>> transferMoney(Map<String, Object> transferRequest) {
        String path = "/api/transfers";
        logRequest("POST", path, transferRequest);
        
        return handleResponse(
                addCommonHeaders(webClient.post().uri(path), apiKey)
                        .bodyValue(transferRequest)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("POST", path, response)),
                "transferMoney"
        );
    }

    /**
     * Get transaction history from Bank6
     */
    public Mono<Map<String, Object>> getTransactionHistory(String accountNumber, 
                                                          String fromDate, 
                                                          String toDate) {
        String path = String.format("/api/accounts/%s/transactions?from=%s&to=%s", 
                                   accountNumber, fromDate, toDate);
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "getTransactionHistory"
        );
    }

    /**
     * Check account balance from Bank6
     */
    public Mono<Map<String, Object>> checkBalance(String accountNumber) {
        String path = "/api/accounts/" + accountNumber + "/balance";
        logRequest("GET", path, null);
        
        return handleResponse(
                addCommonHeaders(webClient.get().uri(path), apiKey)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(response -> logResponse("GET", path, response)),
                "checkBalance"
        );
    }
}
