package com.kienlongbank.app_sale.controller;

import com.kienlongbank.app_sale.dto.request.CreateUserRequest;
import com.kienlongbank.app_sale.dto.request.UpdateUserRequest;
import com.kienlongbank.app_sale.dto.response.ApiResponse;
import com.kienlongbank.app_sale.dto.response.UserResponse;
import com.kienlongbank.app_sale.entity.User;
import com.kienlongbank.app_sale.handler.CreateUserHandler;
import com.kienlongbank.app_sale.handler.UpdateUserHandler;
import com.kienlongbank.app_sale.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * REST Controller for User operations
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
public class UserController extends BaseController {

    private final UserService userService;
    private final CreateUserHandler createUserHandler;
    private final UpdateUserHandler updateUserHandler;

    /**
     * Get all users with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER_READ')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getAllUsers(Pageable pageable) {
        log.info("Getting all users with pagination: {}", pageable);
        
        Page<User> users = userService.findAll(pageable);
        Page<UserResponse> userResponses = users.map(UserResponse::fromEntity);
        
        return success(userResponses);
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER_READ')")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long id) {
        log.info("Getting user by id: {}", id);
        
        User user = userService.findByIdOrThrow(id);
        UserResponse response = UserResponse.fromEntity(user);
        
        return success(response);
    }

    /**
     * Create a new user
     */
    @PostMapping
    @PreAuthorize("hasRole('USER_CREATE')")
    public ResponseEntity<ApiResponse<UserResponse>> createUser(@Valid @RequestBody CreateUserRequest request) {
        log.info("Creating user with username: {}", request.getUsername());
        
        UserResponse response = createUserHandler.handle(request);
        
        return created("User created successfully", response);
    }

    /**
     * Update an existing user
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER_UPDATE')")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(@PathVariable Long id, 
                                                              @Valid @RequestBody UpdateUserRequest request) {
        log.info("Updating user with id: {}", id);
        
        UserResponse response = updateUserHandler.handle(id, request);
        
        return success("User updated successfully", response);
    }

    /**
     * Delete a user (soft delete)
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER_DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        log.info("Deleting user with id: {}", id);
        
        userService.deleteById(id);
        
        return success("User deleted successfully");
    }

    /**
     * Search users by name or email
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER_READ')")
    public ResponseEntity<ApiResponse<List<UserResponse>>> searchUsers(@RequestParam String q) {
        log.info("Searching users with term: {}", q);
        
        List<User> users = userService.searchUsers(q);
        List<UserResponse> responses = users.stream()
                .map(UserResponse::fromEntity)
                .collect(Collectors.toList());
        
        return success(responses);
    }

    /**
     * Get users by status
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('USER_READ')")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getUsersByStatus(@PathVariable User.UserStatus status) {
        log.info("Getting users by status: {}", status);
        
        List<User> users = userService.findByStatus(status);
        List<UserResponse> responses = users.stream()
                .map(UserResponse::fromEntity)
                .collect(Collectors.toList());
        
        return success(responses);
    }

    /**
     * Get users by department
     */
    @GetMapping("/department/{department}")
    @PreAuthorize("hasRole('USER_READ')")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getUsersByDepartment(@PathVariable String department) {
        log.info("Getting users by department: {}", department);
        
        List<User> users = userService.findByDepartment(department);
        List<UserResponse> responses = users.stream()
                .map(UserResponse::fromEntity)
                .collect(Collectors.toList());
        
        return success(responses);
    }

    /**
     * Change user status
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('USER_UPDATE')")
    public ResponseEntity<ApiResponse<UserResponse>> changeUserStatus(@PathVariable Long id, 
                                                                    @RequestParam User.UserStatus status) {
        log.info("Changing user status for id: {} to: {}", id, status);
        
        User user = userService.changeUserStatus(id, status);
        UserResponse response = UserResponse.fromEntity(user);
        
        return success("User status updated successfully", response);
    }

    /**
     * Get current user profile
     */
    @GetMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentUser() {
        String username = getCurrentUser();
        log.info("Getting current user profile for: {}", username);
        
        User user = userService.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("Current user not found"));
        UserResponse response = UserResponse.fromEntity(user);
        
        return success(response);
    }
}
