package com.kienlongbank.app_sale.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.kienlongbank.app_sale.constant.ResponseCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Standard API response format for both success and error cases
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    private String code;
    private String message;
    private T data;
    private LocalDateTime timestamp;
    private String requestId;

    // Success response methods
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code(ResponseCode.SUCCESS)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .code(ResponseCode.SUCCESS)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static <T> ApiResponse<T> success(String message) {
        return ApiResponse.<T>builder()
                .code(ResponseCode.SUCCESS)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static <T> ApiResponse<T> created(T data) {
        return ApiResponse.<T>builder()
                .code(ResponseCode.CREATED)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static <T> ApiResponse<T> created(String message, T data) {
        return ApiResponse.<T>builder()
                .code(ResponseCode.CREATED)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    // Error response methods
    public static <T> ApiResponse<T> error(String code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    public static <T> ApiResponse<T> error(String code, String message, T data) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    // Convenience methods for common errors
    public static <T> ApiResponse<T> badRequest(String message) {
        return error(ResponseCode.INVALID_REQUEST, message);
    }

    public static <T> ApiResponse<T> notFound(String message) {
        return error(ResponseCode.RESOURCE_NOT_FOUND, message);
    }

    public static <T> ApiResponse<T> unauthorized(String message) {
        return error(ResponseCode.UNAUTHORIZED, message);
    }

    public static <T> ApiResponse<T> forbidden(String message) {
        return error(ResponseCode.FORBIDDEN, message);
    }

    public static <T> ApiResponse<T> conflict(String message) {
        return error(ResponseCode.RESOURCE_CONFLICT, message);
    }

    public static <T> ApiResponse<T> internalServerError(String message) {
        return error(ResponseCode.INTERNAL_SERVER_ERROR, message);
    }

    public static <T> ApiResponse<T> serviceUnavailable(String message) {
        return error(ResponseCode.SERVICE_UNAVAILABLE, message);
    }

    // Check if response is successful
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.equals(code) || ResponseCode.CREATED.equals(code)
               || ResponseCode.UPDATED.equals(code) || ResponseCode.DELETED.equals(code);
    }
}
