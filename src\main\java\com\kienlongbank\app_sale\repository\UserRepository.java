package com.kienlongbank.app_sale.repository;

import com.kienlongbank.app_sale.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for User entity using native SQL queries
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find all active users (not deleted)
     */
    @Query(value = "SELECT * FROM users WHERE deleted = false ORDER BY created_at DESC", nativeQuery = true)
    List<User> findAllActive();

    /**
     * Find user by ID and not deleted
     */
    @Query(value = "SELECT * FROM users WHERE id = :id AND deleted = false", nativeQuery = true)
    Optional<User> findByIdActive(@Param("id") Long id);

    /**
     * Find user by username
     */
    @Query(value = "SELECT * FROM users WHERE username = :username AND deleted = false", nativeQuery = true)
    Optional<User> findByUsername(@Param("username") String username);

    /**
     * Find user by email
     */
    @Query(value = "SELECT * FROM users WHERE email = :email AND deleted = false", nativeQuery = true)
    Optional<User> findByEmail(@Param("email") String email);

    /**
     * Find users by status
     */
    @Query(value = "SELECT * FROM users WHERE status = :status AND deleted = false ORDER BY created_at DESC", nativeQuery = true)
    List<User> findByStatus(@Param("status") String status);

    /**
     * Find users by department
     */
    @Query(value = "SELECT * FROM users WHERE department = :department AND deleted = false ORDER BY created_at DESC", nativeQuery = true)
    List<User> findByDepartment(@Param("department") String department);

    /**
     * Check if username exists
     */
    @Query(value = "SELECT COUNT(*) > 0 FROM users WHERE username = :username AND deleted = false", nativeQuery = true)
    boolean existsByUsername(@Param("username") String username);

    /**
     * Check if email exists
     */
    @Query(value = "SELECT COUNT(*) > 0 FROM users WHERE email = :email AND deleted = false", nativeQuery = true)
    boolean existsByEmail(@Param("email") String email);

    /**
     * Check if user exists by ID and is active
     */
    @Query(value = "SELECT COUNT(*) > 0 FROM users WHERE id = :id AND deleted = false", nativeQuery = true)
    boolean existsByIdActive(@Param("id") Long id);

    /**
     * Search users by name or email
     */
    @Query(value = "SELECT * FROM users WHERE " +
           "(LOWER(first_name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(last_name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND deleted = false ORDER BY created_at DESC", nativeQuery = true)
    List<User> searchUsers(@Param("searchTerm") String searchTerm);

    /**
     * Soft delete user by ID
     */
    @Modifying
    @Query(value = "UPDATE users SET deleted = true, updated_at = CURRENT_TIMESTAMP WHERE id = :id", nativeQuery = true)
    int softDeleteById(@Param("id") Long id);

    /**
     * Count active users
     */
    @Query(value = "SELECT COUNT(*) FROM users WHERE deleted = false", nativeQuery = true)
    long countActive();
}
