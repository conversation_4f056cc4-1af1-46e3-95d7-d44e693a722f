package com.kienlongbank.app_sale.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration for RestTemplate with connection pooling and timeouts
 */
@Slf4j
@Configuration
public class RestTemplateConfig {

    @Value("${app.http-client.connection-timeout:30000}")
    private int connectionTimeout;

    @Value("${app.http-client.read-timeout:30000}")
    private int readTimeout;

    @Value("${app.http-client.max-connections:200}")
    private int maxConnections;

    @Value("${app.http-client.max-connections-per-route:20}")
    private int maxConnectionsPerRoute;

    @Value("${app.http-client.connection-time-to-live:60000}")
    private int connectionTimeToLive;

    /**
     * Configure RestTemplate with connection pooling and timeouts
     */
    @Bean
    public RestTemplate restTemplate() {
        log.info("Configuring RestTemplate with connection pooling");

        // Configure connection pool
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxConnections);
        connectionManager.setDefaultMaxPerRoute(maxConnectionsPerRoute);

        // Configure request timeouts
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(connectionTimeout))
                .setResponseTimeout(Timeout.ofMilliseconds(readTimeout))
                .build();

        // Create HTTP client
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();

        // Create request factory
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(connectionTimeout);
        requestFactory.setConnectionRequestTimeout(connectionTimeout);

        // Create RestTemplate
        RestTemplate restTemplate = new RestTemplate(requestFactory);

        log.info("RestTemplate configured successfully");
        log.info("Connection timeout: {}ms", connectionTimeout);
        log.info("Read timeout: {}ms", readTimeout);
        log.info("Max connections: {}", maxConnections);
        log.info("Max connections per route: {}", maxConnectionsPerRoute);

        return restTemplate;
    }
}
