package com.kienlongbank.app_sale.service;

import com.kienlongbank.app_sale.entity.BaseEntity;
import com.kienlongbank.app_sale.exception.ApiException;
import com.kienlongbank.app_sale.repository.BaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Base service class with common CRUD operations
 */
@Slf4j
public abstract class BaseService<T extends BaseEntity, R extends BaseRepository<T>> {

    protected final R repository;

    protected BaseService(R repository) {
        this.repository = repository;
    }

    /**
     * Find all active entities
     */
    @Transactional(readOnly = true)
    public List<T> findAll() {
        log.debug("Finding all active entities");
        return repository.findAllActive();
    }

    /**
     * Find all entities with pagination
     */
    @Transactional(readOnly = true)
    public Page<T> findAll(Pageable pageable) {
        log.debug("Finding all entities with pagination: {}", pageable);
        return repository.findAll(pageable);
    }

    /**
     * Find entity by id
     */
    @Transactional(readOnly = true)
    public Optional<T> findById(Long id) {
        log.debug("Finding entity by id: {}", id);
        return repository.findByIdActive(id);
    }

    /**
     * Find entity by id or throw exception
     */
    @Transactional(readOnly = true)
    public T findByIdOrThrow(Long id) {
        return findById(id)
                .orElseThrow(() -> ApiException.notFound("Entity not found with id: " + id));
    }

    /**
     * Save entity
     */
    @Transactional
    public T save(T entity) {
        log.debug("Saving entity: {}", entity.getClass().getSimpleName());
        validateEntity(entity);
        return repository.save(entity);
    }

    /**
     * Update entity
     */
    @Transactional
    public T update(T entity) {
        log.debug("Updating entity with id: {}", entity.getId());
        if (entity.getId() == null) {
            throw ApiException.badRequest("Entity id cannot be null for update operation");
        }
        
        if (!repository.existsByIdActive(entity.getId())) {
            throw ApiException.notFound("Entity not found with id: " + entity.getId());
        }
        
        validateEntity(entity);
        return repository.save(entity);
    }

    /**
     * Soft delete entity by id
     */
    @Transactional
    public void deleteById(Long id) {
        log.debug("Soft deleting entity with id: {}", id);
        if (!repository.existsByIdActive(id)) {
            throw ApiException.notFound("Entity not found with id: " + id);
        }
        
        int deletedCount = repository.softDeleteById(id);
        if (deletedCount == 0) {
            throw ApiException.internalServerError("Failed to delete entity with id: " + id);
        }
    }

    /**
     * Soft delete multiple entities by ids
     */
    @Transactional
    public void deleteByIds(List<Long> ids) {
        log.debug("Soft deleting entities with ids: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw ApiException.badRequest("Entity ids cannot be null or empty");
        }
        
        int deletedCount = repository.softDeleteByIds(ids);
        log.debug("Deleted {} entities", deletedCount);
    }

    /**
     * Check if entity exists by id
     */
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        log.debug("Checking if entity exists with id: {}", id);
        return repository.existsByIdActive(id);
    }

    /**
     * Count all active entities
     */
    @Transactional(readOnly = true)
    public long count() {
        log.debug("Counting all active entities");
        return repository.countActive();
    }

    /**
     * Validate entity before save/update
     * Override this method in subclasses for custom validation
     */
    protected void validateEntity(T entity) {
        if (entity == null) {
            throw ApiException.badRequest("Entity cannot be null");
        }
    }
}
