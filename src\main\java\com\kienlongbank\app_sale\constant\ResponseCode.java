package com.kienlongbank.app_sale.constant;

/**
 * Response codes for API responses
 * Format: API000 for success, API001-API999 for errors
 */
public final class ResponseCode {
    
    // Success codes
    public static final String SUCCESS = "API000";
    public static final String CREATED = "API001";
    public static final String UPDATED = "API002";
    public static final String DELETED = "API003";
    
    // General error codes (API100-API199)
    public static final String GENERAL_ERROR = "API100";
    public static final String INVALID_REQUEST = "API101";
    public static final String VALIDATION_ERROR = "API102";
    public static final String MISSING_PARAMETER = "API103";
    public static final String INVALID_PARAMETER = "API104";
    public static final String BUSINESS_RULE_VIOLATION = "API105";
    
    // Authentication & Authorization errors (API200-API299)
    public static final String UNAUTHORIZED = "API200";
    public static final String FORBIDDEN = "API201";
    public static final String TOKEN_EXPIRED = "API202";
    public static final String TOKEN_INVALID = "API203";
    public static final String INSUFFICIENT_PRIVILEGES = "API204";
    
    // Resource errors (API300-API399)
    public static final String RESOURCE_NOT_FOUND = "API300";
    public static final String RESOURCE_ALREADY_EXISTS = "API301";
    public static final String RESOURCE_CONFLICT = "API302";
    public static final String RESOURCE_LOCKED = "API303";
    public static final String RESOURCE_EXPIRED = "API304";
    
    // User management errors (API400-API499)
    public static final String USER_NOT_FOUND = "API400";
    public static final String USER_ALREADY_EXISTS = "API401";
    public static final String USER_INACTIVE = "API402";
    public static final String USER_SUSPENDED = "API403";
    public static final String USERNAME_ALREADY_EXISTS = "API404";
    public static final String EMAIL_ALREADY_EXISTS = "API405";
    public static final String INVALID_USER_STATUS = "API406";
    
    // External service errors (API500-API599)
    public static final String EXTERNAL_SERVICE_ERROR = "API500";
    public static final String EXTERNAL_SERVICE_TIMEOUT = "API501";
    public static final String EXTERNAL_SERVICE_UNAVAILABLE = "API502";
    public static final String BANK6_SERVICE_ERROR = "API510";
    public static final String MANGO_SERVICE_ERROR = "API520";
    public static final String INTERNAL_SERVICE_ERROR = "API530";
    
    // Database errors (API600-API699)
    public static final String DATABASE_ERROR = "API600";
    public static final String DATABASE_CONNECTION_ERROR = "API601";
    public static final String DATABASE_CONSTRAINT_VIOLATION = "API602";
    public static final String DATABASE_TIMEOUT = "API603";
    public static final String OPTIMISTIC_LOCK_ERROR = "API604";
    
    // System errors (API700-API799)
    public static final String INTERNAL_SERVER_ERROR = "API700";
    public static final String SERVICE_UNAVAILABLE = "API701";
    public static final String CONFIGURATION_ERROR = "API702";
    public static final String FILE_PROCESSING_ERROR = "API703";
    public static final String ENCRYPTION_ERROR = "API704";
    
    // Rate limiting & throttling (API800-API899)
    public static final String RATE_LIMIT_EXCEEDED = "API800";
    public static final String TOO_MANY_REQUESTS = "API801";
    public static final String QUOTA_EXCEEDED = "API802";
    
    // Maintenance & operational (API900-API999)
    public static final String SYSTEM_MAINTENANCE = "API900";
    public static final String FEATURE_DISABLED = "API901";
    public static final String VERSION_NOT_SUPPORTED = "API902";
    
    private ResponseCode() {
        // Utility class - prevent instantiation
    }
}
