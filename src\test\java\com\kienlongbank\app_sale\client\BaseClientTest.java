package com.kienlongbank.app_sale.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kienlongbank.app_sale.constant.ResponseCode;
import com.kienlongbank.app_sale.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for BaseClient functionality
 */
@ExtendWith(MockitoExtension.class)
class BaseClientTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ObjectMapper objectMapper;

    private TestClient testClient;

    @BeforeEach
    void setUp() {
        testClient = new TestClient();
        testClient.restTemplate = restTemplate;
        testClient.objectMapper = objectMapper;
    }

    @Test
    void testCallApiExternal_Success() {
        // Given
        String endpoint = "http://example.com/api/test";
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> request = Map.of("key", "value");
        Map<String, Object> expectedResponse = Map.of("result", "success");

        ResponseEntity<Map> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(restTemplate.exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // When
        Map<String, Object> result = testClient.callApiExternal(endpoint, headers, request, Map.class);

        // Then
        assertEquals(expectedResponse, result);
        verify(restTemplate).exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class));
    }

    @Test
    void testCallApiExternal_HttpClientError() {
        // Given
        String endpoint = "http://example.com/api/test";
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> request = Map.of("key", "value");

        when(restTemplate.exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Bad Request"));

        // When & Then
        ApiException exception = assertThrows(ApiException.class, () -> 
                testClient.callApiExternal(endpoint, headers, request, Map.class));
        
        assertEquals(ResponseCode.INVALID_REQUEST, exception.getErrorCode());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getHttpStatus());
    }

    @Test
    void testCallApiExternal_HttpServerError() {
        // Given
        String endpoint = "http://example.com/api/test";
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> request = Map.of("key", "value");

        when(restTemplate.exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error"));

        // When & Then
        ApiException exception = assertThrows(ApiException.class, () -> 
                testClient.callApiExternal(endpoint, headers, request, Map.class));
        
        assertEquals(ResponseCode.EXTERNAL_SERVICE_ERROR, exception.getErrorCode());
        assertEquals(HttpStatus.BAD_GATEWAY, exception.getHttpStatus());
    }

    @Test
    void testCallApiExternal_Timeout() {
        // Given
        String endpoint = "http://example.com/api/test";
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> request = Map.of("key", "value");

        when(restTemplate.exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new ResourceAccessException("Connection timeout"));

        // When & Then
        ApiException exception = assertThrows(ApiException.class, () -> 
                testClient.callApiExternal(endpoint, headers, request, Map.class));
        
        assertEquals(ResponseCode.EXTERNAL_SERVICE_TIMEOUT, exception.getErrorCode());
        assertEquals(HttpStatus.GATEWAY_TIMEOUT, exception.getHttpStatus());
    }

    @Test
    void testCallApiInternal_Success() {
        // Given
        String endpoint = "http://internal.example.com/api/test";
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> request = Map.of("key", "value");
        Map<String, Object> expectedResponse = Map.of("result", "success");

        ResponseEntity<Map> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(restTemplate.exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);

        // When
        Map<String, Object> result = testClient.callApiInternal(endpoint, headers, request, Map.class);

        // Then
        assertEquals(expectedResponse, result);
        verify(restTemplate).exchange(eq(endpoint), eq(HttpMethod.POST), any(HttpEntity.class), eq(Map.class));
    }

    @Test
    void testCreateHeadersWithApiKey() {
        // Given
        String apiKey = "test-api-key";

        // When
        HttpHeaders headers = testClient.createHeadersWithApiKey(apiKey);

        // Then
        assertEquals(apiKey, headers.getFirst("X-API-Key"));
    }

    @Test
    void testCreateHeadersWithAuth() {
        // Given
        String token = "test-token";

        // When
        HttpHeaders headers = testClient.createHeadersWithAuth(token);

        // Then
        assertEquals("Bearer test-token", headers.getFirst("Authorization"));
    }

    /**
     * Test implementation of BaseClient for testing purposes
     */
    private static class TestClient extends BaseClient {
        // Expose protected methods for testing
        @Override
        public <T> T callApiExternal(String endpoint, HttpHeaders headers, Object request, Class<T> responseType) {
            return super.callApiExternal(endpoint, headers, request, responseType);
        }

        @Override
        public <T> T callApiInternal(String endpoint, HttpHeaders headers, Object request, Class<T> responseType) {
            return super.callApiInternal(endpoint, headers, request, responseType);
        }

        @Override
        public HttpHeaders createHeadersWithApiKey(String apiKey) {
            return super.createHeadersWithApiKey(apiKey);
        }

        @Override
        public HttpHeaders createHeadersWithAuth(String token) {
            return super.createHeadersWithAuth(token);
        }
    }
}
