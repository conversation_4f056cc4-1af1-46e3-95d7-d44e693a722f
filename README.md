# AJE Sale App

Spring Boot application với cấu trúc modular và tích hợp OAuth2 Keycloak.

## Cấu trúc Project

```
src/main/java/com/kienlongbank/app_sale/
├── AjeSaleAppApplication.java          # Main application class
├── config/                             # Configuration classes
│   ├── SecurityConfig.java            # OAuth2 Security configuration
│   ├── JpaConfig.java                  # JPA and auditing configuration
│   ├── LoggingConfig.java              # Logging interceptor configuration
│   └── WebConfig.java                  # Web configuration
├── controller/                         # REST API controllers
│   ├── BaseController.java             # Base controller với common methods
│   └── UserController.java             # User API endpoints
├── service/                           # Business logic layer
│   ├── BaseService.java               # Base service với CRUD operations
│   └── UserService.java               # User business logic
├── repository/                        # Data access layer
│   ├── BaseRepository.java            # Base repository với common queries
│   └── UserRepository.java            # User data access
├── handler/                           # API handlers sử dụng Spring Bus
│   ├── BaseHandler.java               # Base handler class
│   ├── CreateUserHandler.java         # Handler cho tạo user
│   └── UpdateUserHandler.java         # Handler cho cập nhật user
├── client/                            # External API clients
│   ├── BaseClient.java                # Base client với common functionality
│   ├── Bank6Client.java               # Client cho Bank6 service
│   ├── MangoClient.java               # Client cho Mango service
│   └── InternalServiceClient.java     # Client cho Internal service
├── entity/                            # JPA entities
│   ├── BaseEntity.java                # Base entity với audit fields
│   └── User.java                      # User entity
├── dto/                               # Request/Response objects
│   ├── request/
│   │   ├── CreateUserRequest.java     # Request DTO cho tạo user
│   │   └── UpdateUserRequest.java     # Request DTO cho cập nhật user
│   └── response/
│       ├── ApiResponse.java           # Standard API response format
│       ├── ErrorResponse.java         # Error response format
│       └── UserResponse.java          # User response DTO
├── exception/                         # Custom exceptions
│   ├── ApiException.java              # Custom API exception
│   └── GlobalExceptionHandler.java    # Global exception handler
└── interceptor/                       # Interceptors
    └── LoggingInterceptor.java        # HTTP request/response logging
```

## Tính năng chính

### 1. OAuth2 với Keycloak
- Tích hợp Keycloak làm OAuth2 provider
- JWT token validation
- Role-based access control
- CORS configuration

### 2. Database với PostgreSQL
- JPA/Hibernate integration
- Soft delete support
- Audit fields (created_at, updated_at, created_by, updated_by)
- Optimistic locking với version field

### 3. Logging
- Structured JSON logging với Logstash encoder
- MDC context với username và requestId
- HTTP request/response logging
- Configurable log levels

### 4. External Service Integration
- WebClient cho async HTTP calls
- Retry và error handling
- Timeout configuration
- Structured logging cho external calls

### 5. Exception Handling
- Custom ApiException với HTTP status mapping
- Global exception handler
- Validation error handling
- Structured error responses

## Configuration

### Environment Variables

```bash
# Server Configuration
SERVER_PORT=8080
CONTEXT_PATH=/api

# Database Configuration
DATABASE_URL=********************************************
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres

# Keycloak Configuration
KEYCLOAK_ISSUER_URI=http://localhost:8080/realms/aje-sale
KEYCLOAK_CLIENT_ID=aje-sale-app
KEYCLOAK_CLIENT_SECRET=your-client-secret

# External Services
BANK6_BASE_URL=http://localhost:8081
BANK6_API_KEY=your-bank6-api-key
MANGO_BASE_URL=http://localhost:8082
MANGO_API_KEY=your-mango-api-key
INTERNAL_SERVICE_BASE_URL=http://localhost:8083
INTERNAL_SERVICE_API_KEY=your-internal-api-key

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/aje-sale-app.log
```

## API Endpoints

### User Management
- `GET /api/users` - Get all users (paginated)
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user (soft delete)
- `GET /api/users/search?q={term}` - Search users
- `GET /api/users/status/{status}` - Get users by status
- `GET /api/users/department/{department}` - Get users by department
- `PATCH /api/users/{id}/status?status={status}` - Change user status
- `GET /api/users/me` - Get current user profile

### Required Roles
- `USER_READ` - Read user data
- `USER_CREATE` - Create users
- `USER_UPDATE` - Update users
- `USER_DELETE` - Delete users

## Development

### Prerequisites
- Java 17+
- PostgreSQL 12+
- Keycloak 23+

### Running the Application

1. Start PostgreSQL database
2. Start Keycloak server
3. Configure environment variables
4. Run the application:

```bash
./gradlew bootRun
```

### Building

```bash
./gradlew build
```

### Testing

```bash
./gradlew test
```

## Monitoring

- Health check: `GET /actuator/health`
- Metrics: `GET /actuator/metrics`
- Application info: `GET /actuator/info`

## Security

- All endpoints require authentication except `/actuator/health`
- Role-based authorization với Keycloak roles
- CORS configured cho frontend integration
- JWT token validation

## Logging

Logs được structure theo format JSON với các fields:
- `timestamp` - Thời gian log
- `level` - Log level (INFO, DEBUG, ERROR, etc.)
- `logger` - Logger name
- `message` - Log message
- `username` - Current user (từ JWT token)
- `requestId` - Unique request ID
- `stackTrace` - Stack trace cho errors

## External Services

### Bank6 Client
- Account information
- Money transfer
- Transaction history
- Balance checking

### Mango Client
- Notification sending
- SMS sending
- Email sending
- Delivery reports

### Internal Service Client
- User profile management
- System configuration
- Audit logging
- Business rule validation
- Reference data
