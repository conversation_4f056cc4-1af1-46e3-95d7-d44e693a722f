server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:/api}

spring:
  application:
    name: aje-sale-app

  # Database Configuration
  datasource:
    url: ${DATABASE_URL:********************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:20}
      minimum-idle: ${DB_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://localhost:8080/realms/aje-sale}
          jwk-set-uri: ${KEYCLOAK_JWK_SET_URI:http://localhost:8080/realms/aje-sale/protocol/openid_connect/certs}
      client:
        registration:
          keycloak:
            client-id: ${KEYCLOAK_CLIENT_ID:aje-sale-app}
            client-secret: ${KEYCLOAK_CLIENT_SECRET:your-client-secret}
            scope: openid,profile,email
            authorization-grant-type: authorization_code
            redirect-uri: ${KEYCLOAK_REDIRECT_URI:http://localhost:8080/login/oauth2/code/keycloak}
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://localhost:8080/realms/aje-sale}
            user-name-attribute: preferred_username

# Keycloak Configuration
keycloak:
  realm: ${KEYCLOAK_REALM:aje-sale}
  auth-server-url: ${KEYCLOAK_AUTH_SERVER_URL:http://localhost:8080}
  resource: ${KEYCLOAK_CLIENT_ID:aje-sale-app}
  credentials:
    secret: ${KEYCLOAK_CLIENT_SECRET:your-client-secret}
  use-resource-role-mappings: true
  bearer-only: true

# Logging Configuration
logging:
  level:
    com.kienlongbank.app_sale: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:DEBUG}
    org.springframework.web: ${WEB_LOG_LEVEL:INFO}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{username}] [%X{requestId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{username}] [%X{requestId}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:logs/aje-sale-app.log}

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: ${HEALTH_SHOW_DETAILS:when_authorized}

# Application Configuration
app:
  # HTTP Client Configuration
  http-client:
    connection-timeout: ${HTTP_CLIENT_CONNECTION_TIMEOUT:30000}
    read-timeout: ${HTTP_CLIENT_READ_TIMEOUT:30000}
    max-connections: ${HTTP_CLIENT_MAX_CONNECTIONS:200}
    max-connections-per-route: ${HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE:20}
    connection-time-to-live: ${HTTP_CLIENT_CONNECTION_TTL:60000}

  # External Services
  external-services:
    bank6:
      base-url: ${BANK6_BASE_URL:http://localhost:8081}
      api-key: ${BANK6_API_KEY:your-bank6-api-key}
    mango:
      base-url: ${MANGO_BASE_URL:http://localhost:8082}
      api-key: ${MANGO_API_KEY:your-mango-api-key}
    internal-service:
      base-url: ${INTERNAL_SERVICE_BASE_URL:http://localhost:8083}
      api-key: ${INTERNAL_SERVICE_API_KEY:your-internal-api-key}

  # Security
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:4200}
    allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
    allowed-headers: ${CORS_ALLOWED_HEADERS:*}
    allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}
