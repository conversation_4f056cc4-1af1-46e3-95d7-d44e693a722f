package com.kienlongbank.app_sale.exception;

import com.kienlongbank.app_sale.constant.ResponseCode;
import com.kienlongbank.app_sale.dto.response.ApiResponse;
import com.kienlongbank.app_sale.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Global exception handler for handling all exceptions in the application with i18n support
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    private final MessageService messageService;

    @ExceptionHandler(ApiException.class)
    public ResponseEntity<ApiResponse<Object>> handleApiException(ApiException ex, WebRequest request) {
        log.error("API Exception: {}", ex.getErrorCode(), ex);

        // Get localized message
        String localizedMessage = messageService.getMessage(ex.getErrorCode(), ex.getMessageArgs());

        ApiResponse<Object> response = ApiResponse.error(ex.getErrorCode(), localizedMessage, ex.getData());
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, ex.getHttpStatus());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        log.error("Validation Exception: {}", errors);

        String localizedMessage = messageService.getMessage(ResponseCode.VALIDATION_ERROR);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, localizedMessage, errors);
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request) {

        Map<String, String> errors = new HashMap<>();
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        }

        log.error("Constraint Violation Exception: {}", errors);

        String localizedMessage = messageService.getMessage(ResponseCode.VALIDATION_ERROR);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, localizedMessage, errors);
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException ex, WebRequest request) {

        log.error("Access Denied Exception: {}", ex.getMessage());

        String localizedMessage = messageService.getMessage(ResponseCode.FORBIDDEN);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.FORBIDDEN, localizedMessage);
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentialsException(
            BadCredentialsException ex, WebRequest request) {

        log.error("Bad Credentials Exception: {}", ex.getMessage());

        String localizedMessage = messageService.getMessage(ResponseCode.UNAUTHORIZED);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.UNAUTHORIZED, localizedMessage);
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex, WebRequest request) {
        log.error("Unexpected Exception: {}", ex.getMessage(), ex);

        String localizedMessage = messageService.getMessage(ResponseCode.INTERNAL_SERVER_ERROR);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.INTERNAL_SERVER_ERROR, localizedMessage);
        response.setTimestamp(LocalDateTime.now());

        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
