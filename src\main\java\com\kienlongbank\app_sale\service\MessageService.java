package com.kienlongbank.app_sale.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Locale;

/**
 * Service for handling internationalized messages
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageService {

    private final MessageSource messageSource;

    /**
     * Get message by code using current locale
     */
    public String getMessage(String code) {
        return getMessage(code, null, LocaleContextHolder.getLocale());
    }

    /**
     * Get message by code with parameters using current locale
     */
    public String getMessage(String code, Object[] args) {
        return getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * Get message by code with specific locale
     */
    public String getMessage(String code, Locale locale) {
        return getMessage(code, null, locale);
    }

    /**
     * Get message by code with parameters and specific locale
     */
    public String getMessage(String code, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (Exception e) {
            log.warn("Message not found for code: {} and locale: {}", code, locale);
            return code; // Return code as fallback
        }
    }

    /**
     * Get message by code with default message fallback
     */
    public String getMessage(String code, String defaultMessage) {
        return getMessage(code, null, defaultMessage, LocaleContextHolder.getLocale());
    }

    /**
     * Get message by code with parameters and default message fallback
     */
    public String getMessage(String code, Object[] args, String defaultMessage) {
        return getMessage(code, args, defaultMessage, LocaleContextHolder.getLocale());
    }

    /**
     * Get message by code with parameters, default message and specific locale
     */
    public String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        try {
            return messageSource.getMessage(code, args, defaultMessage, locale);
        } catch (Exception e) {
            log.warn("Message not found for code: {} and locale: {}, using default: {}", code, locale, defaultMessage);
            return defaultMessage != null ? defaultMessage : code;
        }
    }

    /**
     * Check if current locale is Vietnamese
     */
    public boolean isVietnamese() {
        Locale currentLocale = LocaleContextHolder.getLocale();
        return "vi".equals(currentLocale.getLanguage());
    }

    /**
     * Check if current locale is English
     */
    public boolean isEnglish() {
        Locale currentLocale = LocaleContextHolder.getLocale();
        return "en".equals(currentLocale.getLanguage());
    }

    /**
     * Get current locale
     */
    public Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }
}
